<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#F5F5F5"
    android:padding="16dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Al Tarjuman"
            android:textStyle="bold"
            android:textSize="18sp"
            android:layout_centerVertical="true"/>

        <ImageView
            android:id="@+id/closeButton"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close"
            android:layout_alignParentEnd="true"
            android:contentDescription="Close" />
    </RelativeLayout>

    <TextView
        android:id="@+id/originalText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textSize="16sp"
        android:hint="Original text will appear here" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#DDDDDD"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/translatedText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textColor="#0066CC"
        android:hint="Translation will appear here" />

</LinearLayout>