# Overlay Service Implementation

## Location
`android/app/src/main/kotlin/com/example/al_tarjuman/services/OverlayService.kt`

## Required Dependencies
```gradle
implementation 'androidx.lifecycle:lifecycle-service:2.6.1'
implementation 'androidx.work:work-runtime-ktx:2.8.1'
```

## Core Components
1. WindowManager for overlay
2. Foreground service with notification
3. Chat app detection using UsageStatsManager
4. Translation API integration

## Permission Setup
```xml
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
<uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>