// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "allow_the_app_to_translate_texts":
            MessageLookupByLibrary.simpleMessage(
                "اسمح للتطبيق بترجمة النصوص تلقائivering"),
        "allow_the_app_to_translate_your_recordings":
            MessageLookupByLibrary.simpleMessage(
                "اسمح للتطبيق بترجمة تسجيلاتك الصوتية تلقائivering"),
        "chat_now": MessageLookupByLibrary.simpleMessage(
            "السماح بالوصول إلى تطبيقات الهاتف."),
        "description_setting_app": MessageLookupByLibrary.simpleMessage(
            "استمتع بتجربة ترجمة فورية وسلسة مع الترجمان، حيث يمكنك تحويل النصوص والكلمات والتسجيلات الصوتية إلى لغتك المفضلة بسهولة وسرعة. بفضل تقنيات الذكاء الاصطناعي المتطورة التي تضمن دقة عالية وتجربة مريحة"),
        "enable_camera": MessageLookupByLibrary.simpleMessage(
            "السماح للتطبيق باستعمال الكاميرا"),
        "enable_microphone": MessageLookupByLibrary.simpleMessage(
            "السماح للتطبيق باستعمال الميكروفون"),
        "enable_pick_file": MessageLookupByLibrary.simpleMessage(
            "السماح للتطبيق باستيراد الملفات من المعرض"),
        "enable_pick_image": MessageLookupByLibrary.simpleMessage(
            "السماح للتطبيق باستيراد الصور من المعرض"),
        "language": MessageLookupByLibrary.simpleMessage("اللغة"),
        "live_translation":
            MessageLookupByLibrary.simpleMessage("اكتب الترجمة هنا..."),
        "micro_now": MessageLookupByLibrary.simpleMessage(
            "حوّل التسجيلات الصوتية إلى تسجيل مترجم باستخدام الذكاء الاصطناعي."),
        "name_app": MessageLookupByLibrary.simpleMessage("الترجمان"),
        "search_language":
            MessageLookupByLibrary.simpleMessage("ابحث عن لغة..."),
        "symbol_appears_on_the_screen":
            MessageLookupByLibrary.simpleMessage("يظهر الرمز على الشاشة"),
        "title_card": MessageLookupByLibrary.simpleMessage(
            "ترجم فورًا مع الترجمان، بفضل قوة الذكاء الاصطناعي."),
        "translate_now": MessageLookupByLibrary.simpleMessage(
            "أستخدم تطبيق الترجمان لترجمة النصوص الآن.")
      };
}
