import 'package:al_tarjuman/Features/Overlay/overlay_control_page.dart';

import '../../Features/Setting/View/setting_page.dart';
import '../../Features/Splash/splash_page.dart';
import 'package:flutter/material.dart';

import '../../Features/Home/View/home_page.dart';
import '../../Features/Translator/translation_page.dart';
class AppRoutes {
  static const String splash = '/';
  static const String home = '/home';
  static const String translate = '/translate';
  static const String setting = '/setting';
  static const String overlay = '/overLay';
  
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return _defaultPageRoute(const SplashPage());
      case AppRoutes.home:
        return _defaultPageRoute(const HomePage());
      case AppRoutes.translate:
        return _defaultPageRoute(const TranslationPage());
      case AppRoutes.setting:
        return _defaultPageRoute(const SettingPage());
      case AppRoutes.overlay:
        return _defaultPageRoute(const OverlayControlPage());
      default:
        return _errorRoute('No route defined for "${settings.name}"');
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }


  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body:
            Center(child: Text(message, style: const TextStyle(fontSize: 18))),
      ),
    );
  }
}
